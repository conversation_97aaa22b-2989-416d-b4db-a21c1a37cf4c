// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "toii_xmtp_flutter",
    platforms: [
        .iOS("16.0")
    ],
    products: [
        .library(name: "toii-xmtp-flutter", targets: ["toii_xmtp_flutter"]),
     
    ],
    dependencies: [
        .package(url: "https://github.com/xmtp/xmtp-ios.git", from: "4.4.0"),
    ],
    targets: [
        .target(
            name: "toii_xmtp_flutter",
            dependencies: [
                .product(name: "XMTPiOS", package: "xmtp-ios"),
            ],
            resources: [
                // If your plugin requires a privacy manifest, for example if it uses any required
                // reason APIs, update the PrivacyInfo.xcprivacy file to describe your plugin's
                // privacy impact, and then uncomment these lines. For more information, see
                // https://developer.apple.com/documentation/bundleresources/privacy_manifest_files
                // .process("PrivacyInfo.xcprivacy"),

                // If you have other resources that need to be bundled with your plugin, refer to
                // the following instructions to add them:
                // https://developer.apple.com/documentation/xcode/bundling-resources-with-a-swift-package
            ]
        ),
    
    ]
)


