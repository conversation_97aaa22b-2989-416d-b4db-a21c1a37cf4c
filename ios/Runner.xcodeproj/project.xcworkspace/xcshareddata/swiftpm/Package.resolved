{"originHash": "d10e2df4f716cb84fd2d60a0462cce0c15420d936b11b98da5a6ba34da1aa8f3", "pins": [{"identity": "connect-swift", "kind": "remoteSourceControl", "location": "https://github.com/bufbuild/connect-swift", "state": {"revision": "a8c984a1077f78e94e0884c5c11683a7f684f92c", "version": "1.0.0"}}, {"identity": "cryptoswift", "kind": "remoteSourceControl", "location": "https://github.com/krzyzanowskim/CryptoSwift.git", "state": {"revision": "e45a26384239e028ec87fbcc788f513b67e10d8f", "version": "1.9.0"}}, {"identity": "swift-atomics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-atomics.git", "state": {"revision": "b601256eab081c0f92f059e12818ac1d4f178ff7", "version": "1.3.0"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "7b847a3b7008b2dc2f47ca3110d8c782fb2e5c7e", "version": "1.3.0"}}, {"identity": "swift-nio", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio.git", "state": {"revision": "a18bddb0acf7a40d982b2f128ce73ce4ee31f352", "version": "2.86.2"}}, {"identity": "swift-nio-http2", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-http2.git", "state": {"revision": "5e9e99ec96c53bc2c18ddd10c1e25a3cd97c55e5", "version": "1.38.0"}}, {"identity": "swift-nio-ssl", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-nio-ssl.git", "state": {"revision": "b2b043a8810ab6d51b3ff4df17f057d87ef1ec7c", "version": "2.34.1"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "2547102afd04fe49f1b286090f13ebce07284980", "version": "1.31.1"}}, {"identity": "swift-system", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-system.git", "state": {"revision": "395a77f0aa927f0ff73941d7ac35f2b46d47c9db", "version": "1.6.3"}}, {"identity": "xmtp-ios", "kind": "remoteSourceControl", "location": "https://github.com/xmtp/xmtp-ios.git", "state": {"revision": "4481eb9228ce9caf0aa705bdfe5d3e5808d4f068", "version": "4.5.0"}}], "version": 3}